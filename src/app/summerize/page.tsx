"use client";
import { useChat } from "@ai-sdk/react";
import { useState, useRef } from "react";

export default function SummarizePage() {
  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    api: "/api/summarize",
  });

  const [maxWords, setMaxWords] = useState(120);
  const [summaryStyle, setSummaryStyle] = useState<
    "concise" | "detailed" | "bullet-points"
  >("concise");
  const [summaryFocus, setSummaryFocus] = useState<
    "general" | "key-points" | "action-items"
  >("general");
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    const allowedTypes = [
      "text/plain",
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (!allowedTypes.includes(file.type)) {
      alert("Please upload a TXT, PDF, or DOCX file.");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      alert("File size must be less than 10MB.");
      return;
    }

    try {
      let content = "";

      if (file.type === "text/plain") {
        content = await file.text();
      } else {
        // For now, show a message that PDF/DOCX processing will be added
        alert(
          "PDF and DOCX file processing will be available soon. Please use text files for now."
        );
        return;
      }

      // Create a summarization request
      const summaryRequest = `Please summarize the following content in ${maxWords} words using a ${summaryStyle} style with ${summaryFocus} focus:\n\n${content}`;

      // Trigger the chat with the file content
      const syntheticEvent = {
        preventDefault: () => {},
        target: { value: summaryRequest },
      } as any;

      handleInputChange(syntheticEvent);

      // Auto-submit after a brief delay
      setTimeout(() => {
        handleSubmit(syntheticEvent);
      }, 100);
    } catch (error) {
      console.error("Error processing file:", error);
      alert("Error processing file. Please try again.");
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim()) {
      alert("Please enter some content to summarize.");
      return;
    }

    // Add summarization parameters to the input
    const enhancedInput = `Please summarize the following content in ${maxWords} words using a ${summaryStyle} style with ${summaryFocus} focus:\n\n${input}`;

    const syntheticEvent = {
      preventDefault: () => {},
      target: { value: enhancedInput },
    } as any;

    handleInputChange(syntheticEvent);
    handleSubmit(syntheticEvent);
  };

  const wordCount = input
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0).length;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            📄 AI Content Summarizer
          </h1>
          <p className="text-gray-600">
            Your specialized summarization assistant! Paste text or upload
            documents (TXT, PDF, DOCX) and get concise summaries with
            customizable word limits and styles.
          </p>
        </header>

        {/* Settings Panel */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Summary Settings
          </h3>

          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Words: {maxWords}
              </label>
              <input
                type="range"
                min="50"
                max="300"
                value={maxWords}
                onChange={(e) => setMaxWords(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>50</span>
                <span>300</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Summary Style
              </label>
              <select
                value={summaryStyle}
                onChange={(e) => setSummaryStyle(e.target.value as any)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="concise">Concise</option>
                <option value="detailed">Detailed</option>
                <option value="bullet-points">Bullet Points</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Focus Area
              </label>
              <select
                value={summaryFocus}
                onChange={(e) => setSummaryFocus(e.target.value as any)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="general">General</option>
                <option value="key-points">Key Points</option>
                <option value="action-items">Action Items</option>
              </select>
            </div>
          </div>
        </div>

        {/* File Upload Area */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div
            className={`p-8 border-2 border-dashed rounded-lg transition-colors ${
              dragActive
                ? "border-blue-500 bg-blue-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="text-center">
              <div className="text-4xl mb-4">📁</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Upload Document
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop your file here, or click to browse
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Choose File
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,.pdf,.docx"
                onChange={handleFileInputChange}
                className="hidden"
              />
              <p className="text-sm text-gray-500 mt-2">
                Supports TXT, PDF, and DOCX files (max 10MB)
              </p>
            </div>
          </div>
        </div>

        {/* Text Input Area */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <form onSubmit={handleCustomSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Or paste your content here:
                </label>
                <textarea
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Paste your text content here for summarization..."
                  disabled={status === "streaming" || status === "submitted"}
                  className="w-full h-40 border border-gray-300 text-black rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-gray-500">
                    Word count: {wordCount}
                  </span>
                  <button
                    type="submit"
                    disabled={
                      status === "streaming" ||
                      status === "submitted" ||
                      !input.trim()
                    }
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    {status === "streaming" ? "Summarizing..." : "Summarize"}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Messages Display */}
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg ${
                message.role === "user"
                  ? "bg-blue-50 border border-blue-200 ml-8"
                  : "bg-white border border-gray-200 mr-8 shadow-sm"
              }`}
            >
              <div className="flex items-start gap-3">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    message.role === "user"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-600 text-white"
                  }`}
                >
                  {message.role === "user" ? "U" : "AI"}
                </div>
                <div className="flex-1">
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-gray-900">
                      {message.content}
                    </div>
                  </div>
                  {message.role === "assistant" && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                        ✓ Summarized
                      </span>
                      <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                        📊 {maxWords} words max
                      </span>
                      <span className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">
                        🎨 {summaryStyle}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}

          {status === "streaming" && (
            <div className="bg-white border border-gray-200 rounded-lg p-4 mr-8 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center text-sm font-bold">
                  AI
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-gray-600">Generating summary...</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {messages.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                Ready to Summarize Your Content?
              </h2>
              <p className="text-gray-500 mb-6">
                Upload a document or paste your text above, and I'll create a
                concise summary with your preferred word limit and style. I only
                help with summarization tasks.
              </p>
              <div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    📄 File Upload
                  </h3>
                  <p className="text-sm text-gray-600">
                    Support for TXT, PDF, and DOCX files up to 10MB
                  </p>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    ⚙️ Customizable
                  </h3>
                  <p className="text-sm text-gray-600">
                    Adjust word limits, styles, and focus areas
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
