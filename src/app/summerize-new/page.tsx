import React from "react";

const SummarizePageNew = () => {
  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#1f1c14] dark group/design-root overflow-x-hidden">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col w-80">
            <div className="flex h-full min-h-[700px] flex-col justify-between bg-[#1f1c14] p-4">
              <div className="flex flex-col gap-4">
                <div className="flex flex-col">
                  <h1 className="text-white text-base font-medium leading-normal">
                    SummAI
                  </h1>
                  <p className="text-[#bfb69b] text-sm font-normal leading-normal">
                    AI-Powered Summaries
                  </p>
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-3 px-3 py-2">
                    <div
                      className="text-white"
                      data-icon="House"
                      data-size="24px"
                      data-weight="regular"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"></path>
                      </svg>
                    </div>
                    <p className="text-white text-sm font-medium leading-normal">
                      Home
                    </p>
                  </div>
                  <div className="flex items-center gap-3 px-3 py-2 rounded-xl bg-[#413b2a]">
                    <div
                      className="text-white"
                      data-icon="FileText"
                      data-size="24px"
                      data-weight="fill"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,176H96a8,8,0,0,1,0-16h64a8,8,0,0,1,0,16Zm0-32H96a8,8,0,0,1,0-16h64a8,8,0,0,1,0,16Zm-8-56V44l44,44Z"></path>
                      </svg>
                    </div>
                    <p className="text-white text-sm font-medium leading-normal">
                      Summarize
                    </p>
                  </div>
                  <div className="flex items-center gap-3 px-3 py-2">
                    <div
                      className="text-white"
                      data-icon="ClockCounterClockwise"
                      data-size="24px"
                      data-weight="regular"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path d="M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"></path>
                      </svg>
                    </div>
                    <p className="text-white text-sm font-medium leading-normal">
                      History
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-white tracking-light text-[32px] font-bold leading-tight min-w-72">
                Summarize Content
              </p>
            </div>
            <div className="pb-3">
              <div className="flex border-b border-[#5d543c] px-4 gap-8">
                <a
                  className="flex flex-col items-center justify-center border-b-[3px] border-b-[#f9f5ea] text-white pb-[13px] pt-4"
                  href="#"
                >
                  <p className="text-white text-sm font-bold leading-normal tracking-[0.015em]">
                    Short Summary
                  </p>
                </a>
                <a
                  className="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#bfb69b] pb-[13px] pt-4"
                  href="#"
                >
                  <p className="text-[#bfb69b] text-sm font-bold leading-normal tracking-[0.015em]">
                    Detailed Summary
                  </p>
                </a>
              </div>
            </div>
            <div className="flex flex-col p-4">
              <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#5d543c] px-6 py-14">
                <div className="flex max-w-[480px] flex-col items-center gap-2">
                  <p className="text-white text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">
                    Drag and drop or browse
                  </p>
                  <p className="text-white text-sm font-normal leading-normal max-w-[480px] text-center">
                    Paste or type your content here
                  </p>
                </div>
                <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#413b2a] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                  <span className="truncate">Summarize</span>
                </button>
              </div>
            </div>
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              Summary
            </h2>
            <p className="text-white text-base font-normal leading-normal pb-3 pt-1 px-4">
              The project, led by Emily Carter, aims to develop a sustainable
              urban farming system in the heart of the city. This initiative
              will transform underutilized spaces into productive gardens,
              providing fresh produce to local communities and promoting
              environmental awareness. The project will also incorporate
              educational programs to teach residents about sustainable
              practices and the benefits of urban agriculture. Key activities
              include site selection, garden design, community engagement, and
              ongoing maintenance. The project's success will be measured by the
              amount of produce generated, community participation, and the
              adoption of sustainable practices by residents.
            </p>
            <div className="flex px-4 py-3 justify-end">
              <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#413b2a] text-white gap-2 pl-4 text-sm font-bold leading-normal tracking-[0.015em]">
                <div
                  className="text-white"
                  data-icon="Copy"
                  data-size="20px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path>
                  </svg>
                </div>
                <span className="truncate">Copy</span>
              </button>
            </div>
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              History
            </h2>
            <div className="p-4">
              <div className="flex items-stretch justify-between gap-4 rounded-xl">
                <div className="flex flex-col gap-1 flex-[2_2_0px]">
                  <p className="text-white text-base font-bold leading-tight">
                    Project Proposal: Urban Farming
                  </p>
                  <p className="text-[#bfb69b] text-sm font-normal leading-normal">
                    Summary of the urban farming project proposal.
                  </p>
                </div>
                <div
                  className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
                  style={{
                    backgroundImage:
                      'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBbYGlMFPt0dNuA8kLgPjQUTDYJwSDaBWv33_Ko1JEtOdEM0nr4pIN_iupMHI2Z9CHrgSUOSaHz5YnShP7-Nnuh_OTB0-EXzKfWBAV2YtUudDcvCxdsTlRmJXK5HEDVxgIXg4D8aOuc3Wc2_lT-ez6rJSawce5_tJYBpS3V_c6Glwi5LMuIJoJ88chNbmzo4OnEUY8STixnuCflenGwKzVW4UfmSnzcjsstR4zFBS-9wzTaFU97o1GBSynTd3F4rnJFeydFt7BLcqo")',
                  }}
                ></div>
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-stretch justify-between gap-4 rounded-xl">
                <div className="flex flex-col gap-1 flex-[2_2_0px]">
                  <p className="text-white text-base font-bold leading-tight">
                    Meeting Notes: Sustainability
                  </p>
                  <p className="text-[#bfb69b] text-sm font-normal leading-normal">
                    Summary of the meeting notes on sustainability.
                  </p>
                </div>
                <div
                  className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
                  style={{
                    backgroundImage:
                      ' url("https://lh3.googleusercontent.com/aida-public/AB6AXuDiEDeISZ5TrVd3hDg0fcv_-Y3oT11WrgogCOIbY_3e6WH_QQ4nyFhobShg8opgxRrvfR08yihNsqtMP4pEr-2uiwivfIshnfDrXafAv_KgIPE1dmqW4Eh16ZT96iRmP3mOE9tK0KefHxdA7ZOLPd-twhstbh0DY__6XHfqosxs5B0bngIbhybMbZVzLus9X0NujB1wDIg6aHkb7sf3Hd2Ybta7Oe-3_XjqCvOlG4dWczNMUjPonrrSoCH89rmTHSQGQEsi67JVExg")',
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummarizePageNew;
