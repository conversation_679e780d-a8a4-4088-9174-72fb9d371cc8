import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">🤖 AI Tools</h1>
          <p className="text-xl text-gray-600">
            Powerful AI-powered tools for your daily tasks
          </p>
        </header>

        <div className="grid md:grid-cols-2 gap-6">
          <Link href="/recipe" className="group">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 transition-all duration-200 hover:shadow-lg hover:border-gray-300 group-hover:scale-105">
              <div className="text-center">
                <div className="text-6xl mb-4">🍳</div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Recipe Generator
                </h2>
                <p className="text-gray-600">
                  Generate delicious recipes from food names. Get complete
                  cooking instructions, ingredients, and tips from your AI chef
                  assistant.
                </p>
                <div className="mt-4 inline-flex items-center text-blue-600 font-medium">
                  Try Recipe Generator →
                </div>
              </div>
            </div>
          </Link>

          <Link href="/summerize" className="group">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 transition-all duration-200 hover:shadow-lg hover:border-gray-300 group-hover:scale-105">
              <div className="text-center">
                <div className="text-6xl mb-4">📄</div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Content Summarizer
                </h2>
                <p className="text-gray-600">
                  Summarize long texts, documents, and articles. Upload files or
                  paste content to get concise summaries with customizable word
                  limits.
                </p>
                <div className="mt-4 inline-flex items-center text-blue-600 font-medium">
                  Try Summarizer →
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
